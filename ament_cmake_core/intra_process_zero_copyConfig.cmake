# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_intra_process_zero_copy_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED intra_process_zero_copy_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(intra_process_zero_copy_FOUND FALSE)
  elseif(NOT intra_process_zero_copy_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(intra_process_zero_copy_FOUND FALSE)
  endif()
  return()
endif()
set(_intra_process_zero_copy_CONFIG_INCLUDED TRUE)

# output package information
if(NOT intra_process_zero_copy_FIND_QUIETLY)
  message(STATUS "Found intra_process_zero_copy: 0.0.0 (${intra_process_zero_copy_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'intra_process_zero_copy' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT intra_process_zero_copy_DEPRECATED_QUIET)
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(intra_process_zero_copy_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "")
foreach(_extra ${_extras})
  include("${intra_process_zero_copy_DIR}/${_extra}")
endforeach()

set(_AMENT_PACKAGE_NAME "intra_process_zero_copy")
set(intra_process_zero_copy_VERSION "0.0.0")
set(intra_process_zero_copy_MAINTAINER "zjy <<EMAIL>>")
set(intra_process_zero_copy_BUILD_DEPENDS "rclcpp" "std_msgs")
set(intra_process_zero_copy_BUILDTOOL_DEPENDS "ament_cmake")
set(intra_process_zero_copy_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs")
set(intra_process_zero_copy_BUILDTOOL_EXPORT_DEPENDS )
set(intra_process_zero_copy_EXEC_DEPENDS "rclcpp" "std_msgs")
set(intra_process_zero_copy_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(intra_process_zero_copy_GROUP_DEPENDS )
set(intra_process_zero_copy_MEMBER_OF_GROUPS )
set(intra_process_zero_copy_DEPRECATED "")
set(intra_process_zero_copy_EXPORT_TAGS)
list(APPEND intra_process_zero_copy_EXPORT_TAGS "<build_type>ament_cmake</build_type>")

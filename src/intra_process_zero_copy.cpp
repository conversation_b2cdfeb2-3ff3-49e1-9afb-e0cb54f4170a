#include <memory>
#include <thread>
#include <chrono>
#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/string.hpp"

using namespace std::chrono_literals;

// 发布者节点类
class PublisherNode : public rclcpp::Node
{
public:
  PublisherNode(rclcpp::NodeOptions options) : Node("publisher_node", options), count_(0)
  {
    // 创建发布者
    publisher_ = this->create_publisher<std_msgs::msg::String>("zero_copy_topic", 10);
    
    // 创建定时器，每秒发布一次消息
    timer_ = this->create_wall_timer(
      1s, [this]() {
        auto message = std::make_unique<std_msgs::msg::String>();
        message->data = "Hello, Zero-Copy! " + std::to_string(count_++);
        
        RCLCPP_INFO(this->get_logger(), "Publishing: '%s'", message->data.c_str());
        
        // 使用unique_ptr发布消息，实现零拷贝
        publisher_->publish(std::move(message));
      });
  }

private:
  rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publisher_;
  rclcpp::TimerBase::SharedPtr timer_;
  size_t count_;
};

// 订阅者节点类
class SubscriberNode : public rclcpp::Node
{
public:
  SubscriberNode(rclcpp::NodeOptions options) : Node("subscriber_node", options)
  {
    // 创建订阅者，使用unique_ptr接收消息
    subscription_ = this->create_subscription<std_msgs::msg::String>(
      "zero_copy_topic", 10,
      [this](std::unique_ptr<std_msgs::msg::String> msg) {
        RCLCPP_INFO(this->get_logger(), "Received: '%s'", msg->data.c_str());
      });
  }

private:
  rclcpp::Subscription<std_msgs::msg::String>::SharedPtr subscription_;
};

int main(int argc, char * argv[])
{
  // 初始化ROS 2
  rclcpp::init(argc, argv);
  
  // 配置节点选项，启用进程内通信
  rclcpp::NodeOptions options;
  options.use_intra_process_comms(true);
  
  // 创建发布者和订阅者节点
  auto publisher_node = std::make_shared<PublisherNode>(options);
  auto subscriber_node = std::make_shared<SubscriberNode>(options);
  
  // 创建多线程执行器
  rclcpp::executors::MultiThreadedExecutor executor;
  
  // 将节点添加到执行器
  executor.add_node(publisher_node);
  executor.add_node(subscriber_node);
  
  // 启动执行器（在两个线程中运行）
  std::thread executor_thread([&executor]() {
    executor.spin();
  });
  
  // 主线程等待5秒后关闭
  std::this_thread::sleep_for(5s);
  
  // 关闭执行器和ROS 2
  executor.cancel();
  rclcpp::shutdown();
  
  // 等待执行器线程结束
  executor_thread.join();
  
  RCLCPP_INFO(rclcpp::get_logger("main"), "程序结束");
  
  return 0;
}